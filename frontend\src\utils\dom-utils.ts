/* eslint-disable @typescript-eslint/no-explicit-any */
import { SheetTestCase, TestCase } from '../types/automate-process.types';

export const extractKeywordsWithSynonyms = (testCases: SheetTestCase[]) => {
  const keywordFrequency = new Map<string, number>();
  const keywords = new Set<string>();
  const synonymMap = {
    login: [
      'signin',
      'sign in',
      'log in',
      'authenticate',
      'credentials',
      'authentication',
      'access',
    ],
    username: ['email', 'user', 'account', 'id', 'userid', 'identifier', 'login name'],
    password: ['pass', 'secret', 'credential', 'passphrase', 'security code', 'pin'],
    submit: ['login', 'enter', 'send', 'continue', 'proceed', 'go', 'confirm', 'apply'],
    logout: ['signout', 'sign out', 'log out', 'exit', 'disconnect', 'end session'],
    register: ['signup', 'sign up', 'create account', 'join', 'enroll', 'new account'],
    click: ['select', 'choose', 'press', 'tap', 'activate', 'trigger'],
    navigate: ['go to', 'visit', 'open', 'browse', 'access', 'load', 'view'],
    back: ['return', 'previous', 'go back', 'backward'],
    forward: ['next', 'advance', 'proceed', 'continue'],
    refresh: ['reload', 'update', 'renew'],
    input: ['enter', 'type', 'fill', 'provide', 'supply', 'insert'],
    select: ['choose', 'pick', 'opt', 'set', 'specify'],
    checkbox: ['check', 'tick', 'mark', 'toggle'],
    radio: ['option', 'select', 'toggle'],
    upload: ['attach', 'upload file', 'browse', 'select file'],
    verify: ['check', 'confirm', 'validate', 'ensure', 'assert', 'test'],
    visible: ['displayed', 'shown', 'present', 'appears', 'viewable', 'rendered'],
    error: ['warning', 'alert', 'notification', 'message', 'invalid'],
    success: ['valid', 'complete', 'confirmed', 'accepted', 'approved'],
    button: ['btn', 'link', 'cta', 'control', 'action'],
    menu: ['dropdown', 'navigation', 'nav', 'options', 'items'],
    search: ['find', 'lookup', 'query', 'filter', 'seek'],
    profile: ['account', 'user profile', 'settings', 'preferences'],
    dashboard: ['home', 'main', 'overview', 'summary', 'control panel'],
    sidebar: ['panel', 'side menu', 'navigation bar', 'side panel'],
    modal: ['dialog', 'popup', 'overlay', 'lightbox', 'window'],
    notification: ['alert', 'message', 'toast', 'banner', 'announcement'],
    save: ['store', 'update', 'commit', 'apply', 'preserve'],
    delete: ['remove', 'clear', 'erase', 'trash', 'discard'],
    edit: ['modify', 'change', 'update', 'alter', 'adjust'],
    cancel: ['abort', 'dismiss', 'close', 'exit', 'discard'],
    payment: ['checkout', 'pay', 'purchase', 'buy', 'transaction'],
    cart: ['basket', 'shopping cart', 'order', 'items'],
    price: ['cost', 'amount', 'fee', 'charge', 'total'],
    checkout: ['payment', 'finish order', 'complete purchase', 'proceed to payment'],
  };

  if (!testCases || !Array.isArray(testCases) || testCases.length === 0) {
    return [];
  }

  testCases.forEach((tc) => {
    const extractWords = (text: string | undefined) => {
      if (!text) return;

      text
        .toLowerCase()
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 3)
        .forEach((word) => {
          keywords.add(word);
          keywordFrequency.set(word, (keywordFrequency.get(word) || 0) + 1);
        });
    };

    extractWords(tc.description);
    /// commenting this out for now as steps are not being used in the testcases
    // if (tc.steps && Array.isArray(tc.steps)) {
    //   tc.steps.forEach(extractWords);
    // }

    extractWords(tc.expectedResult);
  });

  const extractAcronyms = (phrase: string): string | null => {
    if (!phrase || phrase.length < 5) return null;

    const words = phrase.split(/\s+/);
    if (words.length < 2) return null;

    return words
      .map((w) => w.charAt(0))
      .join('')
      .toLowerCase();
  };

  const baseKeywords = Array.from(keywords);

  baseKeywords.forEach((keyword) => {
    for (const [key, synonyms] of Object.entries(synonymMap)) {
      if (keyword.includes(key) || synonyms.some((syn) => keyword.includes(syn))) {
        keywords.add(key);
        keywordFrequency.set(key, (keywordFrequency.get(key) || 0) + 2);

        synonyms.forEach((syn) => {
          keywords.add(syn);
          keywordFrequency.set(syn, (keywordFrequency.get(syn) || 0) + 1);
        });
      }
    }

    // Process multi-word phrases for acronyms
    if (keyword.includes(' ')) {
      const acronym = extractAcronyms(keyword);
      if (acronym && acronym.length >= 2) {
        keywords.add(acronym);
      }
    }
  });

  return Array.from(keywords).sort(
    (a, b) => (keywordFrequency.get(b) || 0) - (keywordFrequency.get(a) || 0),
  );
};

// Memoization cache for Levenshtein calculations with max size to prevent memory leaks
const levenshteinCache = new Map<string, number>();
const MAX_CACHE_SIZE = 10000;

export const fuzzyMatch = (text: string | undefined, pattern: string | undefined): boolean => {
  if (!text || !pattern) return false;

  const normalizedText = text.toLowerCase().trim();
  const normalizedPattern = pattern.toLowerCase().trim();

  if (normalizedText === normalizedPattern) return true;
  if (normalizedText.includes(normalizedPattern)) return true;
  if (normalizedPattern.length <= 3) return normalizedText.includes(normalizedPattern);
  if (normalizedText.startsWith(normalizedPattern)) return true;

  if (normalizedPattern.length < 3 && normalizedText.length > 20) return false;

  if (normalizedPattern.length >= 2 && normalizedPattern.length <= 5) {
    const words = normalizedText.split(/\s+/);
    if (words.length >= 2) {
      const acronym = words.map((w) => w.charAt(0)).join('');
      if (acronym === normalizedPattern) return true;
    }
  }

  try {
    const threshold = 0.7;
    const allowTranspositions = true;

    // Levenshtein distance calculation with caching
    const calculateLevenshtein = (a: string, b: string): number => {
      // Generate consistent cache key that works regardless of parameter order
      const cacheKey = a.length <= b.length ? `${a}:${b}` : `${b}:${a}`;

      if (levenshteinCache.has(cacheKey)) {
        return levenshteinCache.get(cacheKey)!;
      }

      if (levenshteinCache.size > MAX_CACHE_SIZE) {
        // Clear 20% of the cache when it gets too large
        const keysToDelete = Array.from(levenshteinCache.keys()).slice(0, MAX_CACHE_SIZE / 5);
        keysToDelete.forEach((k) => levenshteinCache.delete(k));
      }

      // Early exit for length difference
      const lenDiff = Math.abs(a.length - b.length);
      if (lenDiff / Math.max(a.length, b.length) > 1 - threshold) {
        return Infinity;
      }

      // Two-row approach for memory efficiency
      let prev = Array(b.length + 1)
        .fill(0)
        .map((_, i) => i);
      let curr = Array(b.length + 1).fill(0);

      for (let i = 1; i <= a.length; i++) {
        curr[0] = i;

        for (let j = 1; j <= b.length; j++) {
          const cost = a[i - 1] === b[j - 1] ? 0 : 1;

          let minCost = Math.min(
            prev[j] + 1, // deletion
            curr[j - 1] + 1, // insertion
            prev[j - 1] + cost, // substitution
          );

          // Add transposition check if enabled
          if (
            allowTranspositions &&
            i > 1 &&
            j > 1 &&
            a[i - 1] === b[j - 2] &&
            a[i - 2] === b[j - 1]
          ) {
            minCost = Math.min(minCost, prev[j - 2] + 1);
          }

          curr[j] = minCost;
        }

        [prev, curr] = [curr, prev];
      }

      const result = prev[b.length];
      levenshteinCache.set(cacheKey, result);
      return result;
    };

    // For longer text, check if pattern appears with some edits allowed
    const words = normalizedText.split(/\s+/);
    for (const word of words) {
      if (word.length < 3) continue;

      const distance = calculateLevenshtein(word, normalizedPattern);
      const maxLength = Math.max(word.length, normalizedPattern.length);
      const similarity = (maxLength - distance) / maxLength;

      if (similarity >= threshold) return true;
    }

    // Efficient sliding window for long texts
    if (normalizedText.length > normalizedPattern.length * 3) {
      const windowSize = normalizedPattern.length + 2;
      // Use larger step size for very long texts
      const step = normalizedText.length > 1000 ? 4 : 2;

      for (let i = 0; i <= normalizedText.length - windowSize; i += step) {
        const window = normalizedText.substring(i, i + windowSize);
        const distance = calculateLevenshtein(window, normalizedPattern);
        const similarity = (windowSize - distance) / windowSize;
        if (similarity >= threshold) return true;
      }
    }

    // Compound word matching for camelCase/PascalCase
    if (normalizedPattern.length > 5 && /[A-Z]/.test(pattern)) {
      const camelCaseParts = normalizedPattern
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase()
        .trim()
        .split(/\s+/);

      if (camelCaseParts.length > 1) {
        const matchesAllParts = camelCaseParts.every(
          (part) =>
            normalizedText.includes(part) ||
            words.some((word) => calculateLevenshtein(word, part) <= 1),
        );

        if (matchesAllParts) return true;
      }
    }

    return false;
  } catch (error) {
    console.debug('Error in fuzzy matching:', error);
    // Fallback to simple matching if an error occurs
    return normalizedText.includes(normalizedPattern);
  }
};

export const filterRelevantElements = (elements: any[], keywords: string[]) => {
  if (!elements || !Array.isArray(elements) || elements.length === 0) return [];
  if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
    return elements.filter((el) => {
      const interactiveTypes = ['a', 'button', 'input', 'select', 'textarea', 'form'];
      return interactiveTypes.includes(el.t) || el.role === 'button';
    });
  }

  const INTERACTIVE_BASE_SCORE = 10;
  const ACTION_ELEMENT_SCORE = 15;
  const MAX_KEYWORD_WEIGHT = 5;
  const MIN_RELEVANCE_SCORE = 2;

  // Pre-process keywords for faster matching
  const processedKeywords = keywords.map((k) => k.toLowerCase().trim());

  // Precompute important action words for faster matching
  const actionWords = new Set([
    'submit',
    'login',
    'signin',
    'continue',
    'next',
    'proceed',
    'save',
    'confirm',
    'apply',
    'update',
    'search',
  ]);

  const scoreElement = (el: any) => {
    let score = 0;
    if (!el) return 0;

    // Score for element type (prioritize interactive elements)
    const isInteractive =
      ['a', 'button', 'input', 'select', 'textarea'].includes(el.t) ||
      el.role === 'button' ||
      el.onclick ||
      el.clkbl;

    if (isInteractive) {
      score += INTERACTIVE_BASE_SCORE;

      // Boost score for form elements
      if (el.form || el.formId || (el.p && el.p.includes('form'))) {
        score += 5;
      }

      // Additional score for specific input types
      if (el.t === 'input') {
        const inputType = el.inputType || '';
        if (['submit', 'button'].includes(inputType)) score += 7;
        if (['text', 'email', 'password'].includes(inputType)) score += 5;
        if (inputType === 'checkbox' || inputType === 'radio') score += 4;
      }

      // Boost visible elements
      if (el.vis !== false) score += 3;
    }

    const elementText = el.tx || '';
    const normalizedText = elementText.toLowerCase().trim();

    if (normalizedText) {
      // Direct check for common action elements
      for (const action of actionWords) {
        if (normalizedText.includes(action)) {
          score += ACTION_ELEMENT_SCORE;
          break;
        }
      }

      // Semantic pattern matching for login elements
      if (
        (normalizedText.includes('sign') || normalizedText.includes('log')) &&
        (normalizedText.includes('in') || normalizedText.includes('on'))
      ) {
        score += ACTION_ELEMENT_SCORE;
      }
    }

    // Score for keyword matches in text, attributes, and ARIA properties
    const elementAttributes = [
      normalizedText,
      (el.id || '').toLowerCase(),
      (el.n || '').toLowerCase(),
      (el.p || '').toLowerCase(),
      (el.aria || {}).label ? el.aria.label.toLowerCase() : '',
      (el.role || '').toLowerCase(),
    ];

    // Score keyword matches with gradually decreasing weight
    processedKeywords.forEach((keyword, index) => {
      // Higher weight for earlier keywords (more important)
      const keywordWeight = Math.max(1, MAX_KEYWORD_WEIGHT - Math.floor(index / 5));

      for (const attr of elementAttributes) {
        if (!attr) continue;

        if (attr === keyword) {
          score += 10 * keywordWeight; // Exact match gets highest score
          break;
        }

        if (attr.includes(keyword)) {
          score += 5 * keywordWeight;
          break;
        }

        if (fuzzyMatch(attr, keyword)) {
          score += 3 * keywordWeight;
          break;
        }
      }
    });

    // Penalize hidden elements
    if (el.vis === false || normalizedText.includes('hidden')) {
      score *= 0.5;
    }

    // Boost for elements with high z-index (likely to be in modals/important overlays)
    if (el.z && el.z > 100) {
      score += 3;
    }

    return score;
  };

  try {
    const scoredElements = elements
      .map((el) => ({ element: el, score: scoreElement(el) }))
      .filter((item) => item.score >= MIN_RELEVANCE_SCORE)
      .sort((a, b) => b.score - a.score);

    // Deduplicate by XPath or other unique identifier
    const uniqueElements = Array.from(
      new Map(
        scoredElements.map((item) => [
          item.element.x || item.element.id || JSON.stringify(item.element),
          item.element,
        ]),
      ).values(),
    );

    return uniqueElements;
  } catch (error) {
    console.debug('Error filtering elements:', error);
    // Fallback to basic filtering of interactive elements
    return elements.filter((el) =>
      ['a', 'button', 'input', 'select', 'textarea', 'form', 'div'].includes(el.t),
    );
  }
};

// Helper function to check if domJson has the expected structure
export const hasDomElements = (domJson: unknown): domJson is { el: unknown[] } => {
  return (
    domJson !== null && typeof domJson === 'object' && 'el' in domJson && Array.isArray(domJson.el)
  );
};
