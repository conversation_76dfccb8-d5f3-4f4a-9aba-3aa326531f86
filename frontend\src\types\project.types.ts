export interface Project {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  isDirectlyShared?: boolean;
  googleSheetId?: string;
  googleSheetUrl?: string;
  submoduleColumn?: string;
  submoduleStartRow?: number;
  googleUserEmail?: string;
  caseIdColumn?: string;
  testCaseDescriptionColumn?: string;
  testCaseExpectedResultColumn?: string;
  excludedTabs?: string;
}

export interface ProjectFormData {
  name: string;
  description: string;
  googleSheetUrl?: string;
  submoduleColumn?: string;
  submoduleStartRow?: number;
  caseIdColumn?: string;
  testCaseDescriptionColumn?: string;
  testCaseExpectedResultColumn?: string;
  excludedTabs?: string;
}
