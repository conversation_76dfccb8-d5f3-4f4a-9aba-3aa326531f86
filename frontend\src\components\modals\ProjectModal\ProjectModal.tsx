import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import lang from '../../../languages/lang';
import { ProjectFormData } from '../../../types/project.types';
import ModalCloseIcon from '../common/ModalCloseIcon';
import './ProjectModal.css';

interface ProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: ProjectFormData) => void;
  initialData?: ProjectFormData;
  title: string;
  isLoading?: boolean;
}

export const ProjectModal = ({
  isOpen,
  onClose,
  onSave,
  initialData,
  title,
  isLoading = false,
}: ProjectModalProps) => {
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    googleSheetUrl: '',
    submoduleColumn: '',
    submoduleStartRow: undefined,
    caseIdColumn: '',
    testCaseDescriptionColumn: '',
    testCaseExpectedResultColumn: '',
    excludedTabs: '',
  });
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: initialData?.name || '',
        description: initialData?.description || '',
        googleSheetUrl: initialData?.googleSheetUrl || '',
        submoduleColumn: initialData?.submoduleColumn || '',
        submoduleStartRow: initialData?.submoduleStartRow || undefined,
        caseIdColumn: initialData?.caseIdColumn || '',
        testCaseDescriptionColumn: initialData?.testCaseDescriptionColumn || '',
        testCaseExpectedResultColumn: initialData?.testCaseExpectedResultColumn || '',
        excludedTabs: initialData?.excludedTabs || '',
      });
      setError('');
    } else {
      setFormData({
        name: '',
        description: '',
        googleSheetUrl: '',
        submoduleColumn: '',
        submoduleStartRow: undefined,
        caseIdColumn: '',
        testCaseDescriptionColumn: '',
        testCaseExpectedResultColumn: '',
        excludedTabs: '',
      });
      setError('');
    }
  }, [initialData, isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { id, value } = e.target;

    // Handle numeric input for submoduleStartRow
    if (id === 'submoduleStartRow') {
      const numValue = value ? parseInt(value, 10) : undefined;
      setFormData((prev) => ({ ...prev, [id]: numValue }));
    } else {
      setFormData((prev) => ({ ...prev, [id]: value }));
    }

    if (id !== 'name') setError('');
  };

  const validateForm = (): boolean => {
    if (!formData.name || !formData.name.trim()) {
      setError('Project name cannot be empty');
      return false;
    }
    const trimmedName = formData.name.trim();
    if (trimmedName.length < 3 || trimmedName.length > 255) {
      setError('Project name must be between 3 and 255 characters');
      return false;
    }

    const {
      googleSheetUrl,
      submoduleColumn,
      submoduleStartRow,
      caseIdColumn,
      testCaseDescriptionColumn,
      testCaseExpectedResultColumn,
    } = formData;

    // Google Sheet URL validation
    if (!googleSheetUrl || googleSheetUrl.trim() === '') {
      setError('Google Sheet URL is required.');
      return false;
    }

    if (!googleSheetUrl.includes('docs.google.com/spreadsheets')) {
      setError('Please enter a valid Google Sheets URL.');
      return false;
    }

    // Submodule Column validation
    if (!submoduleColumn) {
      setError('Submodule Column is required.');
      return false;
    }

    if (!/^[A-Z]$/.test(submoduleColumn)) {
      setError('Submodule Column must be a single uppercase letter (A-Z).');
      return false;
    }

    // Submodule Start Row validation
    if (submoduleStartRow === undefined) {
      setError('Start Row is required.');
      return false;
    }

    if (isNaN(submoduleStartRow) || submoduleStartRow < 1) {
      setError('Start Row must be a positive integer.');
      return false;
    }

    // Case ID Column validation
    if (!caseIdColumn) {
      setError('Case ID Column is required.');
      return false;
    }

    if (!/^[A-Z]$/.test(caseIdColumn)) {
      setError('Case ID Column must be a single uppercase letter (A-Z).');
      return false;
    }

    // TestCase Description Column validation
    if (!testCaseDescriptionColumn) {
      setError('TestCase Description Column is required.');
      return false;
    }

    if (!/^[A-Z]$/.test(testCaseDescriptionColumn)) {
      setError('TestCase Description Column must be a single uppercase letter (A-Z).');
      return false;
    }

    // TestCase Expected Result Column validation
    if (!testCaseExpectedResultColumn) {
      setError('TestCases Expected Result Column is required.');
      return false;
    }

    if (!/^[A-Z]$/.test(testCaseExpectedResultColumn)) {
      setError('TestCases Expected Result Column must be a single uppercase letter (A-Z).');
      return false;
    }

    setError('');
    return true;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    const submissionData: ProjectFormData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      ...(formData.googleSheetUrl
        ? {
            googleSheetUrl: formData.googleSheetUrl.trim(),
            submoduleColumn: formData.submoduleColumn || 'A',
            submoduleStartRow: formData.submoduleStartRow || 1,
            caseIdColumn: formData.caseIdColumn || 'B',
            testCaseDescriptionColumn: formData.testCaseDescriptionColumn || 'C',
            testCaseExpectedResultColumn: formData.testCaseExpectedResultColumn || 'D',
            excludedTabs: formData.excludedTabs?.trim() || undefined,
          }
        : {
            googleSheetUrl: undefined,
            submoduleColumn: undefined,
            submoduleStartRow: undefined,
            caseIdColumn: undefined,
            testCaseDescriptionColumn: undefined,
            testCaseExpectedResultColumn: undefined,
            excludedTabs: undefined,
          }),
    };

    onSave(submissionData);
  };

  if (!isOpen) return null;

  return (
    <div className="project-modal-overlay">
      <div className="project-modal-container">
        {isLoading && (
          <div className="project-modal-loading-overlay">
            <div className="project-modal-loading-content">
              <Spin size="large" />
              <span>Saving project...</span>
            </div>
          </div>
        )}
        <div className="modal-header">
          <ModalCloseIcon onClose={onClose} />
          <h2>{title}</h2>
        </div>
        <div className="project-modal-content">
          <form className="padding-lg">
            <div className="modal-form-group">
              <label htmlFor="name">
                Project Name<span className="required">*</span>
              </label>
              <input
                id="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter project name (3-255 characters)"
                maxLength={255}
                autoFocus
                disabled={isLoading}
              />
            </div>
            <div className="modal-form-group">
              <label htmlFor="description">Description</label>
              <textarea
                id="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter project description"
                rows={2}
                disabled={isLoading}
              />
            </div>

            <h3>Google Sheets Integration</h3>

            <div className="modal-form-group">
              <label htmlFor="googleSheetUrl">
                Google Sheet URL<span className="required">*</span>
              </label>
              <input
                id="googleSheetUrl"
                value={formData.googleSheetUrl}
                onChange={handleInputChange}
                placeholder="Enter Google Sheet URL (e.g., https://docs.google.com/spreadsheets/d/...)"
                disabled={isLoading}
              />
            </div>

            <div className="modal-form-group">
              <label htmlFor="excludedTabs">Exclude Tabs (Optional)</label>
              <input
                id="excludedTabs"
                value={formData.excludedTabs}
                onChange={handleInputChange}
                placeholder="Enter tab names to exclude (comma-separated)"
                disabled={isLoading}
              />
            </div>

            <div className="modal-form-row">
              <div className="modal-form-group third">
                <label htmlFor="submoduleColumn">
                  Submodule Column<span className="required">*</span>
                </label>
                <select
                  id="submoduleColumn"
                  value={formData.submoduleColumn}
                  onChange={handleInputChange}
                  disabled={isLoading}
                >
                  <option value="">Select</option>
                  {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map(
                    (letter) => (
                      <option key={letter} value={letter}>
                        {letter}
                      </option>
                    ),
                  )}
                </select>
              </div>

              <div className="modal-form-group third">
                <label htmlFor="submoduleStartRow">
                  Start Row<span className="required">*</span>
                </label>
                <input
                  id="submoduleStartRow"
                  type="number"
                  min="1"
                  value={formData.submoduleStartRow || ''}
                  onChange={handleInputChange}
                  placeholder="Row"
                  disabled={isLoading}
                />
              </div>

              <div className="modal-form-group third">
                <label htmlFor="caseIdColumn">
                  ID Column<span className="required">*</span>
                </label>
                <select
                  id="caseIdColumn"
                  value={formData.caseIdColumn}
                  onChange={handleInputChange}
                  disabled={isLoading}
                >
                  <option value="">Select</option>
                  {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map(
                    (letter) => (
                      <option key={letter} value={letter}>
                        {letter}
                      </option>
                    ),
                  )}
                </select>
              </div>
            </div>

            <div className="modal-form-row">
              <div className="modal-form-group half">
                <label htmlFor="testCaseDescriptionColumn">
                  Description Column<span className="required">*</span>
                </label>
                <select
                  id="testCaseDescriptionColumn"
                  value={formData.testCaseDescriptionColumn}
                  onChange={handleInputChange}
                  disabled={isLoading}
                >
                  <option value="">Select</option>
                  {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map(
                    (letter) => (
                      <option key={letter} value={letter}>
                        {letter}
                      </option>
                    ),
                  )}
                </select>
              </div>

              <div className="modal-form-group half">
                <label htmlFor="testCaseExpectedResultColumn">
                  Expected Result Column<span className="required">*</span>
                </label>
                <select
                  id="testCaseExpectedResultColumn"
                  value={formData.testCaseExpectedResultColumn}
                  onChange={handleInputChange}
                  disabled={isLoading}
                >
                  <option value="">Select</option>
                  {Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)).map(
                    (letter) => (
                      <option key={letter} value={letter}>
                        {letter}
                      </option>
                    ),
                  )}
                </select>
              </div>
            </div>

            {error && <div className="modal-error">{error}</div>}
            <div className="modal-actions">
              <button
                type="button"
                className="btn-secondary"
                onClick={onClose}
                disabled={isLoading}
              >
                {lang.CANCEL_BUTTON}
              </button>
              <button
                type="button"
                className="btn-primary"
                onClick={handleSubmit}
                disabled={isLoading}
              >
                {lang.SAVE_BUTTON}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
