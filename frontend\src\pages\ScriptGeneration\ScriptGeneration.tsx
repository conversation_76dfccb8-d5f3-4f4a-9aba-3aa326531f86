import { PlusOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import { Alert, Button, Spin, Tree, message } from 'antd';
import { DataNode } from 'antd/es/tree';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { CustomDrawer } from '../../components/CustomDrawer';
import { GeneratedCodePage } from '../../components/pages/GeneratedCodePage';
import crawledPageService from '../../services/crawledpage.service';
import ProjectService from '../../services/project.service';
import SubModuleService from '../../services/submodule.service';
import TestScriptService from '../../services/testscript.service';
import TreeService from '../../services/tree.service';
import { CrawledPage } from '../../types/crawled-page.types';
import { Project } from '../../types/project.types';
import { SheetTestCase } from '../../types/automate-process.types';
import CrawledPagesGrid from './components/CrawledPagesGrid';
import './ScriptGeneration.css';
import ConfirmModal from '../../components/modals/common/ConfirmModal';
import {
  extractKeywordsWithSynonyms,
  filterRelevantElements,
  hasDomElements,
} from '../../utils/dom-utils';
import { LLmService } from '../../services/llm.service';
import { filteroutTestScriptFromLLMResponse } from '../../utils/common.utils';
import { getSystemPrompt } from '../../prompts/prompts';
import lang from '../../languages/lang';

const ScriptGeneration: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [project, setProject] = useState<Project | null>(null);
  const [treeData, setTreeData] = useState<DataNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [isUrlDrawerOpen, setIsUrlDrawerOpen] = useState<boolean>(false);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [savedCode, setSavedCode] = useState<string>('');
  const [crawledPages, setCrawledPages] = useState<CrawledPage[]>([]);
  const [selectedCrawledPages, setSelectedCrawledPages] = useState<React.Key[]>([]);
  const [isCodeGenerationDrawerOpen, setIsCodeGenerationDrawerOpen] = useState<boolean>(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState<boolean>(false);
  const [generatedCode, setGeneratedCode] = useState<string>('');
  const [codeStreamContent, setCodeStreamContent] = useState<string>('');
  const [codeStreamStarted, setCodeStreamStarted] = useState<boolean>(false);
  const [drawerHasUnacceptedTestScript, setDrawerHasUnacceptedTestScript] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Fetch tree structure and crawled pages summary on component mount
  useEffect(() => {
    fetchProject();
    fetchTreeData();
    fetchCrawledPagesSummary();
  }, [projectId]);

  const fetchProject = async () => {
    try {
      if (!projectId) return;

      const response = await ProjectService.getProjectById(parseInt(projectId, 10));
      setProject(response);
    } catch (error) {
      console.error('Error fetching project:', error);
      message.error('Failed to load project details');
    }
  };

  // Helper function to get all keys from tree data for expansion
  const getAllTreeKeys = (nodes: DataNode[]): React.Key[] => {
    const keys: React.Key[] = [];
    const traverse = (node: DataNode) => {
      keys.push(node.key);
      if (node.children) {
        node.children.forEach(traverse);
      }
    };
    nodes.forEach(traverse);
    return keys;
  };

  const fetchTreeData = async () => {
    try {
      if (!projectId) return;

      setIsLoading(true);
      const response = await TreeService.getProjectTree(parseInt(projectId, 10));

      // Transform the tree data for Ant Design Tree component
      const transformedTree = TreeService.transformTreeForAntDesign(response);
      const treeDataArray = [transformedTree as unknown as DataNode];

      setTreeData(treeDataArray);

      // Set all keys as expanded to ensure tree is fully expanded
      const allKeys = getAllTreeKeys(treeDataArray);
      setExpandedKeys(allKeys);

      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching tree data:', error);
      message.error('Failed to load project structure');
      setIsLoading(false);
    }
  };

  // Fetch crawled pages summary (without DOMJson for performance)
  const fetchCrawledPagesSummary = async () => {
    try {
      if (!projectId) return;

      setIsLoading(true);
      const pagesSummary = await crawledPageService.getByProjectIdSummary(parseInt(projectId, 10));

      const pagesWithCrawlStatus = pagesSummary.map((page) => ({
        ...page,
        isCrawled: true,
      }));
      setCrawledPages(pagesWithCrawlStatus);
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching crawled pages summary:', error);
      message.error('Failed to load crawled pages');
      setIsLoading(false);
    }
  };

  // Fetch test script when a submodule is selected
  const fetchTestScript = async (nodeKey: string) => {
    try {
      // Extract entity type and ID from the node key
      const [type, idStr] = nodeKey.split('-');
      const id = parseInt(idStr, 10);

      if (type === 'SUBMODULE') {
        // Fetch test script for the selected submodule
        const response = await TestScriptService.getBySubmoduleId(id);
        if (response) {
          setSavedCode(response?.code || '');
        } else {
          setSavedCode('');
        }
      } else {
        // Clear the code if a non-submodule node is selected
        setSavedCode('');
      }
    } catch (error) {
      console.error('Error fetching test script:', error);
      message.error('Failed to load test script');
      setSavedCode('');
    }
  };

  // Handle tree node selection
  const handleSelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const nodeKey = selectedKeys[0].toString();
      setSelectedNode(nodeKey);
      fetchTestScript(nodeKey);
    } else {
      setSelectedNode(null);
      setSavedCode('');
    }
  };

  const isGenerateButtonDisabled = (): boolean => {
    return !(selectedNode && selectedNode.startsWith('SUBMODULE'));
  };

  const refreshFromGoogleSheet = async () => {
    try {
      if (!projectId) return;

      setIsRefreshing(true);
      message.loading('Refreshing data from Google Sheet...', 0);

      // Call the API to refresh data from Google Sheet
      await ProjectService.refreshFromGoogleSheet(parseInt(projectId, 10));

      // Refresh the tree data (this will also update expanded keys)
      await fetchTreeData();

      message.destroy();
      message.success('Successfully refreshed data from Google Sheet');
    } catch (error) {
      console.error('Error refreshing from Google Sheet:', error);
      message.destroy();
      message.error('Failed to refresh data from Google Sheet');
    } finally {
      setIsRefreshing(false);
    }
  };

  const onClickGenerateScript = async () => {
    if (selectedCrawledPages.length === 0) {
      ConfirmModal.confirm({
        title: 'URL Required',
        content:
          'Please select at least one URL and ensure it has been crawled before proceeding to the next step.',
        okText: 'Page URL',
        cancelText: 'Cancel',
        onOk: () => {
          setIsUrlDrawerOpen(true);
        },
      });
      return;
    }

    try {
      const fetchedtestCases = await fetchTestCases();
      if (!fetchedtestCases) return;

      // Generate code based on test cases
      if (fetchedtestCases && fetchedtestCases.length > 0) {
        // Fetch selected crawled pages with DOMJson
        const selectedPageIds = selectedCrawledPages.map((key) => parseInt(key.toString(), 10));
        const selectedPagesWithDom = await crawledPageService.getByIds(selectedPageIds);

        // Open the code generation drawer, start streaming and generate code
        await generateCode(fetchedtestCases, selectedPagesWithDom);
      } else {
        message.warning('No test cases found in the specified range');
      }
    } catch (error) {
      console.error('Error generating test script:', error);
      message.destroy();
      message.error(
        'Failed to generate test cases: ' +
          (error instanceof Error ? error.message : 'Unknown error'),
      );
      setIsGeneratingCode(false);
    }
  };

  const fetchTestCases = async () => {
    try {
      if (!selectedNode) {
        message.error('Please select a submodule from the tree');
        return;
      }
      // Extract entity type and ID from the node key
      const [type, idStr] = selectedNode.split('-');
      const id = parseInt(idStr, 10);

      if (type !== 'SUBMODULE') {
        message.error('Please select a submodule from the tree');
        return;
      }
      message.loading('Generating test cases from Google Sheet...', 0);

      // Call the API to generate test cases
      const response = await SubModuleService.generateTestCases(id);

      message.destroy();
      message.success(`Successfully generated ${response.testCases.length} test cases`);
      return response.testCases;
    } catch (error) {
      console.error('Error fetching test cases:', error);
      message.error('Failed to load test cases');
      return;
    } finally {
      message.destroy();
    }
  };

  const generateCode = async (
    sheetTestCases: SheetTestCase[],
    selectedPagesWithDom: CrawledPage[],
  ) => {
    try {
      // Open the code generation drawer and start streaming
      setDrawerHasUnacceptedTestScript(true);
      setIsCodeGenerationDrawerOpen(true);
      setIsGeneratingCode(true);
      setCodeStreamContent('');
      setCodeStreamStarted(false);
      setGeneratedCode('');
      setErrorMessage('');

      let fullStreamContent = '';
      let streamHasStarted = false;
      const handleStreamChunk = (chunk: string) => {
        if (!streamHasStarted) {
          setCodeStreamStarted(true);
          streamHasStarted = true;
        }
        fullStreamContent += chunk;
        setCodeStreamContent(fullStreamContent);
      };

      const testCaseKeywords = extractKeywordsWithSynonyms(sheetTestCases as SheetTestCase[]);

      const allDomJsons = selectedPagesWithDom
        .filter((page) => page.isCrawled && page.domJson)
        .map((page) => ({
          ...page.domJson,
          el: Array.isArray(page?.domJson?.el)
            ? filterRelevantElements(page?.domJson?.el, testCaseKeywords)
            : [],
          url: page.pageUrl,
        }));

      // Build the prompt with test cases and filtered DOM structure
      const promptData = {
        testCases: sheetTestCases,
        domJson: allDomJsons, // Now contains only relevant elements
      };

      message.loading('Generating test script...', 0);
      // Call the LLM service with streaming, passing language and platform separately
      await LLmService.generateCode(
        JSON.stringify(promptData),
        getSystemPrompt(),
        handleStreamChunk,
      );

      const filteredCode = filteroutTestScriptFromLLMResponse(fullStreamContent);

      if (filteredCode) {
        setGeneratedCode(filteredCode);
      } else {
        setDrawerHasUnacceptedTestScript(false);
        message.error('Failed to generate or parse valid test script.');
      }

      if (streamHasStarted) {
        setDrawerHasUnacceptedTestScript(true);
      }

      setIsGeneratingCode(false);
      message.destroy();
      message.success('Successfully generated test script');
    } catch (error) {
      console.error('Error generating test script:', error);
      message.error('Failed to generate test script');
      setErrorMessage('Failed to generate test script');
    } finally {
      setIsGeneratingCode(false);
      message.destroy();
    }
  };

  const handleAcceptCode = async () => {
    if (!selectedNode || !generatedCode) return;

    try {
      const [type, idStr] = selectedNode.split('-');
      const id = parseInt(idStr, 10);

      if (type === 'SUBMODULE') {
        setSavedCode('');
        // Check if test script already exists for this submodule
        const existingScript = await TestScriptService.getBySubmoduleId(id);

        if (existingScript) {
          // Update existing test script
          await TestScriptService.updateTestScript(existingScript.id as number, {
            ...existingScript,
            code: generatedCode,
          });
        } else {
          // Create new test script
          await TestScriptService.createTestScript({
            ...createEmptyTestScript(id),
            code: generatedCode,
          });
        }

        setSavedCode(generatedCode);
        setIsCodeGenerationDrawerOpen(false);
        setDrawerHasUnacceptedTestScript(false);
        message.success('Test script saved successfully');
      }
    } catch (error) {
      console.error('Error saving test script:', error);
      message.error('Failed to save test script');
      setErrorMessage('Failed to save test script');
    }
  };

  const createEmptyTestScript = (submoduleId: number) => {
    return {
      code: '',
      subModuleId: submoduleId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  };

  const toggleCodeGenerationDrawer = () => {
    setIsCodeGenerationDrawerOpen(!isCodeGenerationDrawerOpen);
  };

  return (
    <div className="script-generation-container">
      <div className="script-generation-header">
        <h1>{project?.name || 'Script Generation'}</h1>
      </div>
      <div className="script-generation-content">
        {/* Left side - Tree structure */}
        <div className="script-generation-tree">
          {isLoading ? (
            <div className="loading-container">
              <Spin size="large" />
            </div>
          ) : (
            <>
              <div className="tree-header">
                <h2>Project Structure</h2>
                <div className="tree-header-buttons">
                  <Button
                    icon={<SyncOutlined spin={isRefreshing} />}
                    onClick={refreshFromGoogleSheet}
                    title="Refresh from Google Sheet"
                    loading={isRefreshing}
                    disabled={isRefreshing}
                  />
                </div>
              </div>
              <Tree
                expandedKeys={expandedKeys}
                onExpand={setExpandedKeys}
                showLine
                showIcon
                onSelect={handleSelect}
                treeData={treeData}
              />
            </>
          )}
        </div>

        {/* Right side - Generated code */}
        <div className="script-generation-code">
          <div className="code-header">
            <h2>Test Script</h2>
            <div className="code-header-buttons">
              <button
                className="btn-black"
                onClick={() => {
                  setIsUrlDrawerOpen(true);
                }}
              >
                {<PlusOutlined />} Add Page URL
              </button>
              <button
                className="btn-primary"
                disabled={isGenerateButtonDisabled()}
                onClick={onClickGenerateScript}
              >
                {savedCode ? 'Regenerate' : 'Generate'}
              </button>
            </div>
          </div>

          {savedCode && savedCode.length > 0 ? (
            <GeneratedCodePage code={savedCode} language="TypeScript" platform="Playwright" />
          ) : (
            <div className="no-script-selected">
              <p>Select a submodule from the tree to view or generate a test script.</p>
            </div>
          )}
        </div>
      </div>

      {/* URL Drawer */}
      <CustomDrawer
        title="Page URL Management"
        isOpen={isUrlDrawerOpen}
        onClose={() => setIsUrlDrawerOpen(false)}
        onToggle={() => setIsUrlDrawerOpen(!isUrlDrawerOpen)}
        position="right"
        width="50%"
        showToggleButton={false}
      >
        <CrawledPagesGrid
          projectId={parseInt(projectId!, 10)}
          crawledPages={crawledPages}
          isLoading={isLoading}
          fetchCrawledPages={fetchCrawledPagesSummary}
          onClose={() => setIsUrlDrawerOpen(false)}
          selectedRowKeys={selectedCrawledPages}
          setSelectedRowKeys={setSelectedCrawledPages}
        />
      </CustomDrawer>

      <CustomDrawer
        title="Generating Test Script"
        isOpen={isCodeGenerationDrawerOpen}
        onClose={() => setIsCodeGenerationDrawerOpen(false)}
        onToggle={toggleCodeGenerationDrawer}
        position="right"
        width="45%"
        autoScrollToBottom={true}
        isLoading={isGeneratingCode}
        showToggleButton={drawerHasUnacceptedTestScript}
        hasStreamingStarted={codeStreamStarted}
        extraAction={{
          show: true,
          label: lang.ACCEPT_BUTTON,
          onClick: handleAcceptCode,
          disabled: !generatedCode || isGeneratingCode,
          tooltip: isGeneratingCode
            ? 'Generation in progress...'
            : !generatedCode
            ? 'Waiting for code generation to complete'
            : 'Accept the generated script',
        }}
      >
        {codeStreamContent.length > 0 && (
          <div className="streaming-code-container">
            <div className="streaming-code-block">
              {codeStreamContent.split('\n').map((line, index) => (
                <div key={index} className="streaming-code-line">
                  <span className="streaming-line-number">{index + 1}</span>
                  <span className="streaming-line-content">{line}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        {errorMessage.length > 0 && <Alert message={errorMessage} type="error" />}
      </CustomDrawer>
    </div>
  );
};

export default ScriptGeneration;
